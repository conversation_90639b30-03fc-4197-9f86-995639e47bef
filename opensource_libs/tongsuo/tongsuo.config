# Tongsuo configuration for trusty-tee integration
# Based on OpenSSL configuration pattern

CONFIGURE_ARGS="\
-DL_ENDIAN \
no-apps \
no-camellia \
no-capieng \
no-cast \
no-dtls1 \
no-gost \
no-gmp \
no-heartbeats \
no-idea \
no-jpake \
no-md2 \
no-mdc2 \
no-rc5 \
no-rdrand \
no-ripemd \
no-rfc3779 \
no-rsax \
no-sctp \
no-seed \
no-sha0 \
no-static_engine \
no-whirlpool \
no-zlib \
no-ssl \
no-tls \
no-dtls \
enable-sm2 \
enable-sm3 \
enable-sm4 \
"

# configure arguments specific for 32-bit arch
CONFIGURE_ARGS_32="\
linux-generic32 \
"

# configure arguments specific for 64-bit arch
CONFIGURE_ARGS_64="\
linux-generic64 \
"

# configure arguments specific for static build
CONFIGURE_ARGS_STATIC="\
no-dso \
"

# unneeded directories for crypto-only build
UNNEEDED_SOURCES="\
MacOS \
Netware \
VMS \
apps \
demos \
doc \
engines \
ms \
os2 \
perl \
shlib \
ssl \
test \
times \
tools \
util \
providers/fips \
providers/legacy \
"

# unneeded files
UNNEEDED_SOURCES+="\
CHANGES \
CHANGES.md \
Configure \
FAQ \
INSTALL \
INSTALL.md \
LICENSE \
Makefile \
NEWS \
PROBLEMS \
README \
README.md \
config \
configdata.pm.in \
"

NEEDED_SOURCES="\
crypto \
include \
providers/common \
providers/implementations \
"

# Arch-specific compiler defines for crypto/ library.
#
TONGSUO_CRYPTO_DEFINES="\
NO_WINDOWS_BRAINDEATH \
TONGSUO_CRYPTO \
"

TONGSUO_CRYPTO_CLANG_ASFLAGS_arm="\
-no-integrated-as \
"

TONGSUO_CRYPTO_DEFINES_arm="\
AES_ASM \
GHASH_ASM \
OPENSSL_BN_ASM_GF2m \
OPENSSL_BN_ASM_MONT \
OPENSSL_CPUID_OBJ \
SHA1_ASM \
SHA256_ASM \
SHA512_ASM \
SM3_ASM \
SM4_ASM \
"

TONGSUO_CRYPTO_CLANG_ASFLAGS_arm64="\
-no-integrated-as \
"

TONGSUO_CRYPTO_DEFINES_arm64="\
OPENSSL_CPUID_OBJ \
SHA1_ASM \
SHA256_ASM \
SHA512_ASM \
SM3_ASM \
SM4_ASM \
"

TONGSUO_CRYPTO_DEFINES_mips="\
OPENSSL_BN_ASM_MONT \
AES_ASM \
SHA1_ASM \
SHA256_ASM \
"

TONGSUO_CRYPTO_DEFINES_mips32r6="\
OPENSSL_NO_ASM \
"

TONGSUO_CRYPTO_DEFINES_mips64="\
OPENSSL_NO_ASM \
"

TONGSUO_CRYPTO_DEFINES_x86="\
AES_ASM \
DES_PTR \
DES_RISC1 \
DES_UNROLL \
GHASH_ASM \
MD5_ASM \
OPENSSL_BN_ASM_GF2m \
OPENSSL_BN_ASM_MONT \
OPENSSL_BN_ASM_PART_WORDS \
OPENSSL_CPUID_OBJ \
OPENSSL_IA32_SSE2 \
RC4_INDEX \
SHA1_ASM \
SHA256_ASM \
SHA512_ASM \
SM3_ASM \
SM4_ASM \
"

TONGSUO_CRYPTO_DEFINES_x86_64="\
AES_ASM \
DES_UNROLL \
GHASH_ASM \
MD5_ASM \
OPENSSL_BN_ASM_GF2m \
OPENSSL_BN_ASM_MONT \
OPENSSL_BN_ASM_MONT5 \
OPENSSL_CPUID_OBJ \
OPENSSL_IA32_SSE2 \
SHA1_ASM \
SHA256_ASM \
SHA512_ASM \
SM3_ASM \
SM4_ASM \
"

TONGSUO_CRYPTO_INCLUDES="\
. \
include \
crypto \
crypto/include \
providers/common/include \
providers/implementations/include \
"

# Source files will be generated by Python script
TONGSUO_CRYPTO_SOURCES=""

# Arch-specific source files will be generated by Python script
TONGSUO_CRYPTO_SOURCES_arm=""
TONGSUO_CRYPTO_SOURCES_EXCLUDES_arm=""

TONGSUO_CRYPTO_SOURCES_arm64=""
TONGSUO_CRYPTO_SOURCES_EXCLUDES_arm64=""

TONGSUO_CRYPTO_SOURCES_mips=""
TONGSUO_CRYPTO_SOURCES_EXCLUDES_mips=""

TONGSUO_CRYPTO_SOURCES_mips32r6=""
TONGSUO_CRYPTO_SOURCES_EXCLUDES_mips32r6=""

TONGSUO_CRYPTO_SOURCES_mips64=""
TONGSUO_CRYPTO_SOURCES_EXCLUDES_mips64=""

TONGSUO_CRYPTO_SOURCES_x86=""
TONGSUO_CRYPTO_SOURCES_EXCLUDES_x86=""

TONGSUO_CRYPTO_SOURCES_x86_64=""
TONGSUO_CRYPTO_SOURCES_EXCLUDES_x86_64=""

# Load trusty-specific configuration
source ./tongsuo.trusty.config
