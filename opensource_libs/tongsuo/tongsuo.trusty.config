# Tongsuo trusty-specific configuration
# Optimized for TEE environment with crypto-only functionality

CONFIGURE_ARGS_TRUSTY="\
-DL_ENDIAN \
linux-generic32:::<trusty_std.h> \
--api=1.1.1 \
--symbol-prefix=tongsuo_ \
no-apps \
no-camellia \
no-capieng \
no-cast \
no-cms \
no-comp \
no-conf \
no-des \
no-dso \
no-dtls1 \
no-err \
no-gost \
no-gmp \
no-heartbeats \
no-idea \
no-jpake \
no-krb5 \
no-locking \
no-md2 \
no-md4 \
no-mdc2 \
no-ocsp \
no-pem \
no-pkcs12 \
no-pqueue \
no-rc2 \
no-rc5 \
no-rdrand \
no-rfc3779 \
no-ripemd \
no-rsax \
no-sctp \
no-seed \
no-sha0 \
no-srp \
no-ssl \
no-tls \
no-dtls \
no-static_engine \
no-store \
no-threads \
no-ts \
no-txt_db \
no-ui \
no-whirlpool \
enable-sm2 \
enable-sm3 \
enable-sm4 \
"

# Trusty-specific compiler defines for crypto/ library.
#
TONGSUO_CRYPTO_TRUSTY_DEFINES="\
GETPID_IS_MEANINGLESS \
NO_WINDOWS_BRAINDEATH \
TONGSUO_CRYPTO \
OPENSSL_SYS_TRUSTY \
"

TONGSUO_CRYPTO_TRUSTY_DEFINES_arm="\
OPENSSL_BN_ASM_GF2m \
OPENSSL_BN_ASM_MONT \
GHASH_ASM \
AES_ASM \
SHA1_ASM \
SHA256_ASM \
SHA512_ASM \
SM3_ASM \
SM4_ASM \
"

TONGSUO_CRYPTO_TRUSTY_DEFINES_arm64="\
OPENSSL_CPUID_OBJ \
SHA1_ASM \
SHA256_ASM \
SHA512_ASM \
SM3_ASM \
SM4_ASM \
"

TONGSUO_CRYPTO_TRUSTY_DEFINES_mips=""

TONGSUO_CRYPTO_TRUSTY_DEFINES_x86=""

TONGSUO_CRYPTO_TRUSTY_DEFINES_x86_64=""

TONGSUO_CRYPTO_TRUSTY_INCLUDES="\
. \
include \
crypto \
crypto/include \
providers/common/include \
providers/implementations/include \
"

# Core crypto sources for trusty environment
# This will be populated by the Python script based on Tongsuo Makefile analysis
TONGSUO_CRYPTO_TRUSTY_SOURCES=""

TONGSUO_CRYPTO_TRUSTY_SOURCES_arm=""
TONGSUO_CRYPTO_TRUSTY_SOURCES_EXCLUDES_arm=""

TONGSUO_CRYPTO_TRUSTY_SOURCES_arm64=""
TONGSUO_CRYPTO_TRUSTY_SOURCES_EXCLUDES_arm64=""

TONGSUO_CRYPTO_TRUSTY_SOURCES_mips=""
TONGSUO_CRYPTO_TRUSTY_SOURCES_EXCLUDES_mips=""

TONGSUO_CRYPTO_TRUSTY_SOURCES_x86=""
TONGSUO_CRYPTO_TRUSTY_SOURCES_EXCLUDES_x86=""

TONGSUO_CRYPTO_TRUSTY_SOURCES_x86_64=""
TONGSUO_CRYPTO_TRUSTY_SOURCES_EXCLUDES_x86_64=""
