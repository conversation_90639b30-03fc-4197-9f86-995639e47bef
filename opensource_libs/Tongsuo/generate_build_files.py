#!/usr/bin/env python3
"""
Tongsuo build files generator for trusty-tee integration
Based on OpenSSL's generate_build_files.py pattern

This script analyzes Tongsuo's build.info files and generates
Crypto-config-trusty.mk with source file lists for trusty environment.
"""

import os
import re
import sys
from pathlib import Path

class TongsuoBuildGenerator:
    def __init__(self, tongsuo_root):
        self.tongsuo_root = Path(tongsuo_root)
        self.crypto_sources = []
        self.crypto_includes = []
        self.arch_sources = {
            'arm': [],
            'arm64': [],
            'x86': [],
            'x86_64': [],
            'mips': [],
            'mips64': []
        }
        self.arch_excludes = {
            'arm': [],
            'arm64': [],
            'x86': [],
            'x86_64': [],
            'mips': [],
            'mips64': []
        }

    def parse_build_info(self, build_info_path):
        """Parse a build.info file and extract source files"""
        sources = []
        if not build_info_path.exists():
            return sources
            
        with open(build_info_path, 'r') as f:
            content = f.read()
            
        # Extract SOURCE lines for libcrypto
        source_pattern = r'SOURCE\[\.\.\/libcrypto\]\s*=\s*(.+?)(?=\n\S|\n$|\Z)'
        matches = re.findall(source_pattern, content, re.MULTILINE | re.DOTALL)
        
        for match in matches:
            # Clean up the match and split by whitespace
            files = re.sub(r'\\|\n', ' ', match).split()
            for file in files:
                file = file.strip()
                if file and not file.startswith('$') and file.endswith('.c'):
                    sources.append(file)
                    
        return sources

    def scan_crypto_directory(self):
        """Scan crypto directory for source files"""
        crypto_dir = self.tongsuo_root / 'crypto'
        
        # Core crypto sources from main build.info
        main_build_info = crypto_dir / 'build.info'
        if main_build_info.exists():
            self.crypto_sources.extend(self.parse_build_info(main_build_info))
        
        # Scan subdirectories
        subdirs = [
            'aes', 'asn1', 'bio', 'bn', 'buffer', 'cmac', 'dh', 'dsa', 
            'ec', 'evp', 'hmac', 'lhash', 'modes', 'objects', 'rand',
            'rsa', 'sha', 'sm2', 'sm3', 'sm4', 'stack', 'x509'
        ]
        
        for subdir in subdirs:
            subdir_path = crypto_dir / subdir
            if subdir_path.exists():
                build_info = subdir_path / 'build.info'
                sources = self.parse_build_info(build_info)
                
                # Add subdirectory prefix to sources
                for source in sources:
                    if not source.startswith('crypto/'):
                        source = f'crypto/{subdir}/{source}'
                    self.crypto_sources.append(source)
                    
                # Also scan for .c files directly
                for c_file in subdir_path.glob('*.c'):
                    rel_path = f'crypto/{subdir}/{c_file.name}'
                    if rel_path not in self.crypto_sources:
                        self.crypto_sources.append(rel_path)

    def generate_crypto_config_mk(self):
        """Generate Crypto-config-trusty.mk file"""
        
        # Remove duplicates and sort
        self.crypto_sources = sorted(list(set(self.crypto_sources)))
        
        # Filter out unwanted sources for trusty environment
        filtered_sources = []
        exclude_patterns = [
            'apps/', 'ssl/', 'test/', 'engines/', 'providers/fips',
            'providers/legacy', 'dllmain.c', 'o_fopen.c', 'o_dir.c',
            'threads_win.c', 'uid.c', 'o_time.c'
        ]
        
        for source in self.crypto_sources:
            should_exclude = False
            for pattern in exclude_patterns:
                if pattern in source:
                    should_exclude = True
                    break
            if not should_exclude:
                filtered_sources.append(source)
        
        self.crypto_sources = filtered_sources
        
        # Generate the .mk file content
        content = self.generate_mk_content()
        
        output_file = self.tongsuo_root / 'Crypto-config-trusty.mk'
        with open(output_file, 'w') as f:
            f.write(content)
            
        print(f"Generated {output_file}")
        print(f"Total crypto sources: {len(self.crypto_sources)}")

    def generate_mk_content(self):
        """Generate the content for Crypto-config-trusty.mk"""
        content = """# Auto-generated - DO NOT EDIT!
# To regenerate, run:
#     python3 generate_build_files.py
#
# This script will append to the following variables:
#
#    LOCAL_CFLAGS
#    LOCAL_C_INCLUDES
#    LOCAL_SRC_FILES_$(TARGET_ARCH)
#    LOCAL_SRC_FILES_$(TARGET_2ND_ARCH)
#    LOCAL_CFLAGS_$(TARGET_ARCH)
#    LOCAL_CFLAGS_$(TARGET_2ND_ARCH)
#    LOCAL_ADDITIONAL_DEPENDENCIES
#    LOCAL_EXPORT_C_INCLUDE_DIRS


LOCAL_ADDITIONAL_DEPENDENCIES += $(LOCAL_PATH)/Crypto-config-trusty.mk

common_cflags := \\
  -DGETPID_IS_MEANINGLESS \\
  -DNO_WINDOWS_BRAINDEATH \\
  -DTONGSUO_CRYPTO \\

common_src_files := \\
"""
        
        # Add source files
        for source in self.crypto_sources:
            content += f"  {source} \\\n"
        
        content += """
common_c_includes := \\
  external/tongsuo/. \\
  external/tongsuo/crypto \\
  external/tongsuo/crypto/include \\
  external/tongsuo/include \\
  external/tongsuo/include/openssl \\
  external/tongsuo/providers/common/include \\
  external/tongsuo/providers/implementations/include \\

arm_clang_asflags :=

arm_cflags := \\
  -DAES_ASM \\
  -DGHASH_ASM \\
  -DOPENSSL_BN_ASM_GF2m \\
  -DOPENSSL_BN_ASM_MONT \\
  -DSHA1_ASM \\
  -DSHA256_ASM \\
  -DSHA512_ASM \\
  -DSM3_ASM \\
  -DSM4_ASM \\

arm_src_files := \\

arm_exclude_files :=

arm64_clang_asflags :=

arm64_cflags := \\
  -DOPENSSL_CPUID_OBJ \\
  -DSHA1_ASM \\
  -DSHA256_ASM \\
  -DSHA512_ASM \\
  -DSM3_ASM \\
  -DSM4_ASM \\

arm64_src_files := \\

arm64_exclude_files :=

x86_clang_asflags :=

x86_cflags :=

x86_src_files :=

x86_exclude_files :=

x86_64_clang_asflags :=

x86_64_cflags :=

x86_64_src_files :=

x86_64_exclude_files :=

mips_clang_asflags :=

mips_cflags :=

mips_src_files :=

mips_exclude_files :=

mips64_clang_asflags :=

mips64_cflags :=

mips64_src_files :=

mips64_exclude_files :=

mips32r6_clang_asflags :=

mips32r6_cflags :=

mips32r6_src_files :=

mips32r6_exclude_files :=


LOCAL_EXPORT_C_INCLUDE_DIRS := $(LOCAL_PATH)/include

ifdef ARCH_MIPS_REV6
mips_cflags := $(mips32r6_cflags)
mips_src_files := $(mips32r6_src_files)
mips_exclude_files := $(mips32r6_exclude_files)
endif

LOCAL_CFLAGS += $(common_cflags)
LOCAL_C_INCLUDES += $(common_c_includes)

LOCAL_SRC_FILES_arm += $(filter-out $(arm_exclude_files),$(common_src_files) $(arm_src_files))
LOCAL_CFLAGS_arm += $(arm_cflags)
LOCAL_CLANG_ASFLAGS_arm += $(arm_clang_asflags)

LOCAL_SRC_FILES_arm64 += $(filter-out $(arm64_exclude_files),$(common_src_files) $(arm64_src_files))
LOCAL_CFLAGS_arm64 += $(arm64_cflags)
LOCAL_CLANG_ASFLAGS_arm64 += $(arm64_clang_asflags)

LOCAL_SRC_FILES_x86 += $(filter-out $(x86_exclude_files),$(common_src_files) $(x86_src_files))
LOCAL_CFLAGS_x86 += $(x86_cflags)
LOCAL_CLANG_ASFLAGS_x86 += $(x86_clang_asflags)

LOCAL_SRC_FILES_x86_64 += $(filter-out $(x86_64_exclude_files),$(common_src_files) $(x86_64_src_files))
LOCAL_CFLAGS_x86_64 += $(x86_64_cflags)
LOCAL_CLANG_ASFLAGS_x86_64 += $(x86_64_clang_asflags)

LOCAL_SRC_FILES_mips += $(filter-out $(mips_exclude_files),$(common_src_files) $(mips_src_files))
LOCAL_CFLAGS_mips += $(mips_cflags)
LOCAL_CLANG_ASFLAGS_mips += $(mips_clang_asflags)

LOCAL_SRC_FILES_mips64 += $(filter-out $(mips64_exclude_files),$(common_src_files) $(mips64_src_files))
LOCAL_CFLAGS_mips64 += $(mips64_cflags)
LOCAL_CLANG_ASFLAGS_mips64 += $(mips64_clang_asflags)
"""
        
        return content

def main():
    if len(sys.argv) > 1:
        tongsuo_root = sys.argv[1]
    else:
        tongsuo_root = os.path.dirname(os.path.abspath(__file__))
    
    generator = TongsuoBuildGenerator(tongsuo_root)
    generator.scan_crypto_directory()
    generator.generate_crypto_config_mk()

if __name__ == '__main__':
    main()
