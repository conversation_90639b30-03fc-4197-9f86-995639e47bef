# Tongsuo crypto library integration for trusty-tee
# Based on OpenSSL integration pattern

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

TARGET_ARCH := $(ARCH)
TARGET_2ND_ARCH := $(ARCH)

# Reset local variables
LOCAL_CFLAGS :=
LOCAL_C_INCLUDES :=
LOCAL_SRC_FILES_$(TARGET_ARCH) :=
LOCAL_SRC_FILES_$(TARGET_2ND_ARCH) :=
LOCAL_CFLAGS_$(TARGET_ARCH) :=
LOCAL_CFLAGS_$(TARGET_2ND_ARCH) :=
LOCAL_ADDITIONAL_DEPENDENCIES :=

# get tongsuo_cflags
MODULE_SRCDEPS += $(LOCAL_DIR)/build-config-trusty.mk
include $(LOCAL_DIR)/build-config-trusty.mk

# get target_c_flags, target_c_includes, target_src_files
MODULE_SRCDEPS += $(LOCAL_DIR)/Crypto-config-trusty.mk
include $(LOCAL_DIR)/Crypto-config-trusty.mk

MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(LOCAL_SRC_FILES_$(ARCH)))

MODULE_CFLAGS += $(LOCAL_CFLAGS)
MODULE_CFLAGS += $(tongsuo_cflags_trusty)
MODULE_CFLAGS += -Wno-error=implicit-function-declaration
MODULE_CFLAGS += -Wno-empty-body
MODULE_CFLAGS += -Wno-missing-field-initializers
MODULE_CFLAGS += -Wno-macro-redefined

# Define missing macros for trusty environment
MODULE_CFLAGS += -D__GNUC_PREREQ\(maj,min\)=\(\(__GNUC__\>\>maj\)\|\|\(\(__GNUC__==maj\)\&\&\(__GNUC_MINOR__\>=min\)\)\)
MODULE_CFLAGS += -D__glibc_clang_prereq\(maj,min\)=0

# Global for other modules which include tongsuo headers
GLOBAL_DEFINES += OPENSSL_SYS_TRUSTY
GLOBAL_DEFINES += TONGSUO_CRYPTO

LOCAL_C_INCLUDES := $(patsubst external/tongsuo/%,%,$(LOCAL_C_INCLUDES))
GLOBAL_INCLUDES += $(addprefix $(LOCAL_DIR)/,$(LOCAL_C_INCLUDES))

# Add Tongsuo internal headers - order matters!
# Put Tongsuo headers first to avoid conflicts with BoringSSL
MODULE_COMPILEFLAGS += -I$(LOCAL_DIR)/include
MODULE_COMPILEFLAGS += -I$(LOCAL_DIR)/include/internal
MODULE_COMPILEFLAGS += -I$(LOCAL_DIR)/include/crypto
MODULE_COMPILEFLAGS += -I$(LOCAL_DIR)
MODULE_COMPILEFLAGS += -I$(LOCAL_DIR)/crypto

MODULE_INCLUDES += $(LOCAL_DIR)/include
MODULE_INCLUDES += $(LOCAL_DIR)/include/internal
MODULE_INCLUDES += $(LOCAL_DIR)/include/crypto
MODULE_INCLUDES += $(LOCAL_DIR)
MODULE_INCLUDES += $(LOCAL_DIR)/crypto

MODULE_DEPS := \
	user/base/lib/openssl-stubs \
	user/base/lib/libc-rctee

include make/rctee_lib.mk
