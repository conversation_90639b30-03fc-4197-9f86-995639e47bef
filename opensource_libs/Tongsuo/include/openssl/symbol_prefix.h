/*
 * WARNING: do not edit!
 * Generated by Tongsuo configuration for trusty-tee integration
 *
 * Copyright 2016-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OPENSSL_SYMBOL_PREFIX_H
# define OPENSSL_SYMBOL_PREFIX_H
# pragma once

# ifdef  __cplusplus
extern "C" {
# endif

/*
 * Symbol prefix configuration for Tongsuo in trusty-tee
 * This file defines symbol prefixes to avoid conflicts with BoringSSL
 */

# define OPENSSL_SYMBOL_PREFIX tongsuo_

# ifdef  __cplusplus
}
# endif

#endif                          /* OPENSSL_SYMBOL_PREFIX_H */
